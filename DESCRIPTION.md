# TablissNG - A Maintained Fork of the Beautiful New Tab Extension

> **Important**: This is a maintained fork of the original Tabliss project, which was abandoned. I've cloned the repository, merged all existing pull requests, and am actively maintaining it with bug fixes and new features. The original extension is no longer maintained, but this fork keeps <PERSON><PERSON><PERSON><PERSON> alive and improving! Github page: <https://github.com/BookCatKid/TablissNG>

Transform your new tab page into something beautiful and uniquely yours. Choose from many background providers, and be rewarded with stunning, new pictures every time you open a new tab. Add your favourite widgets to stay productive throughout your day. Create your own new tab experience.

We care about your privacy and your experience. TablissNG is private, fast, and completely free alternative to extensions like Momentum.

## Features

- Hundreds of customisation options
- Millions of backgrounds
- Useful widgets that keep you productive
- Translations for 40+ languages
- Private, secure, and requires no permissions
- Settings sync between your devices
- 100% free and open source

## Backgrounds

- **Astronomy Picture of the Day**: Daily astronomy pictures with scientific descriptions
- **Colour Gradient**: Beautiful gradients to brighten your day
- **Custom Upload**: Use your own images or videos for a personal touch
- **GIPHY**: Endless animated GIFs for a dynamic new tab
- **Online Image**: Provide a url to display an image
- **Solid Color**: Simple, clean backgrounds
- **Unsplash Photos**: Millions of high-quality, curated photographs
- **Wikimedia Commons**: Daily image of the day from Wikimedia Commons

## Widgets

 - **Binary Clock**: Display the current time in binary format.
 - **Bitcoin Mempool**: Get the current block height.
 - **Countdown**: Literally counting down the days.
 - **Custom CSS**: Make your new tab more style-ish (advanced users).
 - **Custom HTML**: Add static HTML (advanced users).
 - **Custom Text**: Display random text from a list.
 - **Github Calendar**: Get motivated by green squares.
 - **Greeting**: Be personally greeted all day.
 - **IP Info**: Display your IP address and location details.
 - **Jokes**: Get a laugh with random jokes.
 - **Literature Clock**: Check the time, with sophistication.
 - **Message**: Add your own text.
 - **Notes**: Keep track of your thoughts and ideas.
 - **Quick Links**: Add custom links to your favorite sites.
 - **Quotes**: Be inspired (or not, there's categories).
 - **Search**: Search the web using your favorite engine.
 - **Time Since**: Track time elapsed since a specific date.
 - **Time**: Display the current time in various formats.
 - **todo**: Add reminders to procrastinate.
 - **Weather**: Display current weather conditions for your location.
 - **Work Hours**: Track your working hours and time remaining.

## Development & Contributing

This fork is actively maintained! Found a bug? Have a feature request? Want to contribute? Visit:
<https://github.com/BookCatKid/TablissNG>

Original project by Joel Shepherd ([@joelshepherd](https://github.com/joelshepherd)) - Thank you for creating Tabliss!
