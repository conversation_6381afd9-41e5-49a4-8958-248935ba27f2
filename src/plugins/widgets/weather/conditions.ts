/** Weather conditions for a point in time */
export interface Conditions {
  timestamp: number;
  temperature: number;
  apparentTemperature: number;
  humidity: number;
  weatherCode: number;
}

/** Find conditions for the current time */
export const findCurrent = (
  conditions: Conditions[],
  now: number,
): Conditions | null =>
  conditions
    .slice()
    .reverse()
    .find((condition) => now >= condition.timestamp) ?? null;

/** Map of weatherCodes to icons */
export const weatherCodes: Record<number, string> = {
  0: "sun", // clear sky // TODO: or moon
  1: "sun", // mainly clear // TODO: or moon
  2: "cloud", // party cloudy
  3: "cloud", // overcast
  45: "cloud-rain", // fog
  48: "cloud-rain", // depositing rime fog
  51: "cloud-rain", // light drizzle
  53: "cloud-rain", // moderate drizzle
  55: "cloud-rain", // heavy drizzle
  56: "cloud-rain", // light freezing drizzle
  57: "cloud-rain", // heavy freezing drizzle
  61: "cloud-rain", // light rain
  63: "cloud-rain", // moderate rain
  65: "cloud-rain", // heavy rain
  66: "cloud-rain", // light freezing rain
  67: "cloud-rain", // heavy freezing rain
  71: "cloud-snow", // light snow
  73: "cloud-snow", // moderate snow
  75: "cloud-snow", // heavy snow
  77: "cloud-snow", // snow grains
  80: "cloud-rain", // light rain showers
  81: "cloud-rain", // moderate rain showers
  82: "cloud-rain", // heavy rain showers
  85: "cloud-snow", // light snow showers
  86: "cloud-snow", // heavy snow showers
  95: "cloud-lightning", // thunderstorm
  96: "cloud-lightning", // thunderstorm light hail
  99: "cloud-lightning", // thunderstorm heavy hail
};
