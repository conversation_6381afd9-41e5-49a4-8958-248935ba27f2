import { defineMessages } from "react-intl";

export const messages = defineMessages({
  eventHasArrived: {
    id: "plugins.timeTracker.eventHasArrived",
    defaultMessage: "Event has arrived!",
  },
  day: {
    id: "plugins.timeTracker.day",
    defaultMessage: "day",
  },
  days: {
    id: "plugins.timeTracker.days",
    defaultMessage: "days",
  },
  hour: {
    id: "plugins.timeTracker.hour",
    defaultMessage: "hour",
  },
  hours: {
    id: "plugins.timeTracker.hours",
    defaultMessage: "hours",
  },
  minute: {
    id: "plugins.timeTracker.minute",
    defaultMessage: "minute",
  },
  minutes: {
    id: "plugins.timeTracker.minutes",
    defaultMessage: "minutes",
  },
  second: {
    id: "plugins.timeTracker.second",
    defaultMessage: "second",
  },
  seconds: {
    id: "plugins.timeTracker.seconds",
    defaultMessage: "seconds",
  }
});
