.FloatingSaveButton {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 2em;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  z-index: 1000;
  cursor: grab;
  user-select: none;
  touch-action: none;
  transition: background-color 0.25s ease-out;
}

.FloatingSaveButton:hover {
  background-color: #2980b9;
}

.FloatingSaveButton.dragging {
  cursor: grabbing;
  opacity: 0.9;
  transition: none;
}

/* Dark mode styles */
body.dark .FloatingSaveButton {
  background-color: #2d2d2d;
  color: #ffffff;
}

body.dark .FloatingSaveButton:hover {
  background-color: #3a3a3a;
}

/* Icon size adjustment */
.FloatingSaveButton svg {
  width: 20px;
  height: 20px;
}


