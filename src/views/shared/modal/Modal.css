.Modal-container {
  background: rgba(0, 0, 0, 0.25);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  display: flex;
  align-items: end;
  justify-content: center;
}

.Modal {
  background: white;
  border-radius: 1rem 1rem 0 0;
  padding: 2rem;
  min-width: 300px;
  max-width: 500px;
}

body.dark .Modal {
  background: #1a1a1a;
  color: #ffffff;
}

.Modal-footer {
  text-align: center;
}

/* Add dark mode styles for any links inside modals */
body.dark .Modal {
  color: #ffffff;
}

/* Add dark mode styles for any inputs inside modals */
body.dark .Modal input,
body.dark .Modal select,
body.dark .Modal textarea {
  background-color: #2d2d2d;
  border-color: #444;
  color: #ffffff;
}

/* Add dark mode styles for any buttons inside modals */
body.dark .Modal button {
  background-color: #2d2d2d;
  color: #ffffff;
  border-color: #444;
}

body.dark .Modal button:hover {
  background-color: #3a3a3a;
}
