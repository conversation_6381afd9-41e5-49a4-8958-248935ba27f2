@use "sass:color"

// Dark mode variables
$dark-bg: #2d2d2d
$dark-button-bg: #3a3a3a
$dark-border: #444
$dark-text: #ffffff

.Settings .Widget
    border: none
    border-radius: 0.5rem
    background: #ecf0f1
    margin: 1em 0
    padding: 1em

    .dark &
        background: $dark-bg
        color: $dark-text

    h4
        margin: 0
        
        .dark &
            color: $dark-text

    .title--buttons
        button
            float: right
            margin: -0.25em 0 0 0.5em

            .dark &
                background: $dark-button-bg
                color: $dark-text
                border-color: $dark-border

                &:hover
                    background: color.adjust($dark-button-bg, $lightness: 10%)

        h4
            cursor: pointer

    .settings
        margin: 1.5rem 0

    p:last-child 
        margin-bottom: 0

    .button--icon
        background-color: white

        .dark &
            background-color: $dark-button-bg
            color: $dark-text

            &:hover
                background-color: color.adjust($dark-button-bg, $lightness: 10%)
