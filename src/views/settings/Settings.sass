@use "sass:color"

// Dark mode variables
$dark-bg: #1a1a1a
$dark-input-bg: #2d2d2d
$dark-border: #444
$dark-text: #ffffff
$dark-secondary-text: #dddddd

// Scrollbar colors
$scrollbar-track-light: #F2F2F2
$scrollbar-thumb-light: #BDBDBD
$scrollbar-thumb-hover-light: #6E6E6E
$scrollbar-track-dark: #2d2d2d
$scrollbar-thumb-dark: #444444
$scrollbar-thumb-hover-dark: #555555

::-webkit-scrollbar
    width: 10px

::-webkit-scrollbar-track
    background: $scrollbar-track-light
    .dark &
        background: $scrollbar-track-dark

::-webkit-scrollbar-thumb
    background: $scrollbar-thumb-light
    border-radius: 5px

    .dark &
        background: $scrollbar-thumb-dark

::-webkit-scrollbar-thumb:hover
    background: $scrollbar-thumb-hover-light

    .dark &
        background: $scrollbar-thumb-hover-dark

::-webkit-resizer

    .dark &
        background-color: $dark-border

*
    scrollbar-width: thin
    scrollbar-color: $scrollbar-thumb-light $scrollbar-track-light

    .dark &
        scrollbar-color: $scrollbar-thumb-dark $scrollbar-track-dark

.Settings
    .react-dropdown-select
        .dark &
            border-color: $dark-border !important
            .react-dropdown-select-dropdown
                background: $dark-input-bg
                border: none !important
            .react-dropdown-select-item, .react-dropdown-select-item-selected
                border: none !important

    h1
        margin: 2rem 0
        svg
            fill: #3498db
            width: 100%

    h2, h3, h4
        color: #212121

    h2, h3
        margin-bottom: 0

    .plane
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.25)
        background-color: white
        max-width: 100%
        height: 100%
        overflow-y: auto
        padding: 0 1rem
        position: absolute
        width: 330px
        transition: transform 0.15s ease-out

        // Add dark mode styles for plane
        .dark &
            background-color: $dark-bg
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5)

    label
        display: block
        margin: 1rem 0

        &:last-child
            margin-bottom: 0

        // Add dark mode styles for labels
        .dark &
            color: $dark-secondary-text

    label + .info
        font-size: 0.9em
        margin-top: -0.75rem

        // Add dark mode styles for info text
        .dark &
            color: $dark-secondary-text

    input[type=email],
    input[type=file],
    input[type=number],
    input[type=text],
    input[type=time],
    input[type=date],
    input[type=datetime-local],
    input[type=url],
    textarea,
    select
        border: 1px solid #bdc3c7
        border-radius: 0.2em
        color: inherit
        display: block
        margin: 0.5em 0
        padding: 0.5em
        width: 100%
        overflow: hidden

        // Add dark mode styles for inputs
        .dark &
            background-color: $dark-input-bg
            border-color: $dark-border
            color: $dark-text

            &:focus
                border-color: #3498db
                outline: none

        &.primary
            background-color: #ecf0f1
            border: 0.25em solid #ecf0f1

            // Add dark mode styles for primary inputs
            .dark &
                background-color: $dark-input-bg
                border: 0.25em solid #2d2d2d

    h2, h3, h4
        .dark &
            color: $dark-text

    input[type=radio],
    input[type=checkbox]
        margin-right: 0.5rem

        .dark &
            accent-color: #3498db

    input[type=color]
        display: block
        border: 1px solid #bdc3c7

        .dark &
            background-color: #313738
            border: 1px solid #2b2d2f

    input[type=file]::file-selector-button
        background-color: #d6d8d9
        border: none
        border-radius: 0.2em
        padding: 0.45em 1em
        transition: background-color 0.25s ease-out
        cursor: pointer
        margin-right: 0.5em

        &:hover
            background-color: #bdc3c7

        .dark &
            background-color: color.adjust($dark-input-bg, $lightness: 15%)
            color: $dark-text

            &:hover
                background-color: color.adjust($dark-input-bg, $lightness: 25%)

    .button
        border: 0
        border-radius: 2em
        color: white
        cursor: pointer
        display: inline-block
        padding: 0.5em 1em
        text-decoration: none
        transition: background 0.25s ease-out
        text-align: center
        font-weight: 500

        &:disabled
            background-color: #bdc3c7
            cursor: default

            .dark &
                background-color: $dark-border

        &--primary
            background-color: #3498db
            &:hover
                background: color.adjust(#3498db, $lightness: -10%)
            &:active
                background: color.adjust(#3498db, $lightness: -20%)

        &--secondary
            background: color.adjust(#bdc3c7, $lightness: -20%)
            &:hover
                background: color.adjust(#bdc3c7, $lightness: -30%)
            &:active
                background: color.adjust(#bdc3c7, $lightness: -40%)

            .dark &
                background: color.adjust($dark-border, $lightness: 10%)
                &:hover
                    background: color.adjust($dark-border, $lightness: 20%)
                &:active
                    background: color.adjust($dark-border, $lightness: 30%)

    hr
        border: 1px solid #ecf0f1
        margin: 1rem 0

        .dark &
            border-color: $dark-border

.button--icon
    background-color: #ecf0f1
    border: none
    border-radius: 1em
    display: inline-block
    padding: 0.5em
    line-height: 1
    height: 2em
    width: 2em
    color: black
    cursor: pointer
    transition: background-color 0.25s ease-out

    .dark &
        background-color: $dark-input-bg
        color: $dark-text

    &:hover
        background-color: #bdc3c7 !important

        .dark &
            background-color: color.adjust($dark-input-bg, $lightness: 10%) !important

    &.button--primary
        background-color: #3498db !important
        color: white
        padding: 0.5em

        &:hover
            background-color: color.adjust(#3498db, $lightness: -20%) !important
