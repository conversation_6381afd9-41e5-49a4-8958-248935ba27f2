# next

## todo

- [x] test cases for broken things
- [x] prefix iteration function
- [x] add indexeddb storage
- [ ] measure performance
- [x] handle atomic writes
- [x] wait for storage to populate db
- [ ] handle conflicts
- [ ] tombstone investigation
- [ ] \(way later) handle schemas
- [x] fix react integration (stop duplicating state, remove hacks, etc.)

minor:

- [x] debounce storage events
  - [ ] consider a debounce time
- [ ] possibly use a broadcastchannel to sync changes cross tabs?
- [ ] improve react integration (provider support)
