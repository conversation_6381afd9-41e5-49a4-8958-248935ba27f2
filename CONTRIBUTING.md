# Contributing

I would love you contribute to <PERSON><PERSON><PERSON><PERSON>!

I accept issues and pull requests for both bugs and feature requests.
If your feature is larger (like a new plugin), I suggest creating a
new issue to propose the new feature before you start.

## Submission Guidelines

### Submitting an Issue

When submitting an issue, try to be as detailed as possible.

- For bugs, this could include:
  - The steps required to reproduce the bug.
- For feature requests, this could include:
  - Details of how the new feature should work
  - Links to supporting examples, references or documentation.
